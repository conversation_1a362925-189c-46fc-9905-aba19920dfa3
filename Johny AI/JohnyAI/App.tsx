import React, { useState, useRef, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  AppState,
  SafeAreaView,

  Image,
} from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { LinearGradient } from 'expo-linear-gradient';



interface Attachment {
  id: string;
  name: string;
  uri: string;
  type: 'image' | 'document';
  mimeType?: string;
  size?: number;
}

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
  attachments?: Attachment[];
}

export default function App() {
  // Funkcia na vytvorenie úvodnej správy
  const getInitialMessage = (): Message => ({
    id: '1',
    text: 'Ahoj! Som Arturko AI, tvoj pomocník. Ako ti môžem pomôcť?',
    isUser: false,
    timestamp: new Date(),
  });

  const [messages, setMessages] = useState<Message[]>([getInitialMessage()]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentAttachments, setCurrentAttachments] = useState<Attachment[]>([]);
  const scrollViewRef = useRef<ScrollView>(null);

  // Automatické scrollovanie na koniec pri novej správe
  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [messages]);

  // Resetovanie chatu pri zatvorení/pozadí aplikácie
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        // Resetujeme chat na úvodnú správu
        setMessages([getInitialMessage()]);
        setInputText('');
        setCurrentAttachments([]);
        setIsLoading(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, []);

  // Funkcie pre prílohy
  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const attachment: Attachment = {
          id: Date.now().toString(),
          name: asset.fileName || `image_${Date.now()}.jpg`,
          uri: asset.uri,
          type: 'image',
          mimeType: asset.mimeType,
          size: asset.fileSize,
        };
        setCurrentAttachments(prev => [...prev, attachment]);
      }
    } catch (error) {
      Alert.alert('Chyba', 'Nepodarilo sa načítať obrázok');
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        const attachment: Attachment = {
          id: Date.now().toString(),
          name: asset.name,
          uri: asset.uri,
          type: 'document',
          mimeType: asset.mimeType,
          size: asset.size,
        };
        setCurrentAttachments(prev => [...prev, attachment]);
      }
    } catch (error) {
      Alert.alert('Chyba', 'Nepodarilo sa načítať dokument');
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setCurrentAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const readFileContent = async (attachment: Attachment): Promise<string> => {
    try {
      if (attachment.type === 'image') {
        return `[Obrázok: ${attachment.name}]`;
      } else {
        // Pre textové súbory skúsime načítať obsah
        if (attachment.mimeType?.includes('text') ||
            attachment.name.endsWith('.txt') ||
            attachment.name.endsWith('.md') ||
            attachment.name.endsWith('.json') ||
            attachment.name.endsWith('.js') ||
            attachment.name.endsWith('.ts') ||
            attachment.name.endsWith('.py') ||
            attachment.name.endsWith('.java') ||
            attachment.name.endsWith('.cpp') ||
            attachment.name.endsWith('.c') ||
            attachment.name.endsWith('.html') ||
            attachment.name.endsWith('.css')) {
          const content = await FileSystem.readAsStringAsync(attachment.uri);
          return `[Súbor: ${attachment.name}]\n\nObsah:\n${content}`;
        } else {
          return `[Dokument: ${attachment.name} (${attachment.mimeType || 'neznámy typ'})]`;
        }
      }
    } catch (error) {
      return `[Súbor: ${attachment.name} - nepodarilo sa načítať obsah]`;
    }
  };

  // Funkcia na vymazanie chatu
  const clearChat = () => {
    Alert.alert(
      'Vymazať chat',
      'Naozaj chcete vymazať celú históriu chatu?',
      [
        {
          text: 'Zrušiť',
          style: 'cancel',
        },
        {
          text: 'Vymazať',
          style: 'destructive',
          onPress: () => {
            setMessages([getInitialMessage()]);
            setInputText('');
            setCurrentAttachments([]);
          },
        },
      ]
    );
  };

  const sendMessage = async () => {
    if ((!inputText.trim() && currentAttachments.length === 0) || isLoading) return;

    // Pripravíme text správy s prílohami
    let messageText = inputText.trim();

    // Ak máme prílohy, pridáme ich obsah do správy
    if (currentAttachments.length > 0) {
      const attachmentContents = await Promise.all(
        currentAttachments.map(att => readFileContent(att))
      );

      if (messageText) {
        messageText += '\n\n' + attachmentContents.join('\n\n');
      } else {
        messageText = attachmentContents.join('\n\n');
      }
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim() || 'Prílohy',
      isUser: true,
      timestamp: new Date(),
      attachments: currentAttachments.length > 0 ? [...currentAttachments] : undefined,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setCurrentAttachments([]);
    setIsLoading(true);

    try {
      // Volanie Ollama API
      const response = await fetch('http://************:11434/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'codellama:7b',
          prompt: messageText,
          stream: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: data.response || 'Prepáčte, nepodarilo sa mi vygenerovať odpoveď.',
        isUser: false,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Chyba pri volaní API:', error);
      Alert.alert(
        'Chyba pripojenia',
        'Nepodarilo sa pripojiť k AI serveru. Skontrolujte, či je Ollama server spustený.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const renderMessage = (message: Message) => (
    <View
      key={message.id}
      style={[
        styles.messageContainer,
        message.isUser ? styles.userMessage : styles.aiMessage,
      ]}
    >
      {/* Prílohy */}
      {message.attachments && message.attachments.length > 0 && (
        <View style={styles.attachmentsContainer}>
          {message.attachments.map((attachment) => (
            <View key={attachment.id} style={styles.attachmentItem}>
              {attachment.type === 'image' ? (
                <Image
                  source={{ uri: attachment.uri }}
                  style={styles.attachmentImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.documentAttachment}>
                  <Text style={styles.documentName}>{attachment.name}</Text>
                  <Text style={styles.documentType}>
                    {attachment.mimeType || 'Dokument'}
                  </Text>
                </View>
              )}
            </View>
          ))}
        </View>
      )}

      {/* Text správy */}
      {message.text && (
        <Text style={[
          styles.messageText,
          message.isUser ? styles.userMessageText : styles.aiMessageText,
        ]}>
          {message.text}
        </Text>
      )}

      <Text style={styles.timestamp}>
        {message.timestamp.toLocaleTimeString('sk-SK', {
          hour: '2-digit',
          minute: '2-digit',
        })}
      </Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <LinearGradient
        colors={['#1a1a2e', '#16213e', '#0f3460']}
        style={styles.backgroundGradient}
      >
          <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
          >
            <StatusBar style="light" />

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTextContainer}>
          <Text style={styles.headerTitle}>Arturko AI</Text>
          <Text style={styles.headerSubtitle}>Tvoj pomocník</Text>
        </View>
      </View>

      {/* Messages */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.messagesContainer}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
      >
        {messages.map(renderMessage)}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#007AFF" />
            <Text style={styles.loadingText}>AI premýšľa...</Text>
          </View>
        )}
      </ScrollView>

      {/* Aktuálne prílohy */}
      {currentAttachments.length > 0 && (
        <View style={styles.currentAttachmentsContainer}>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {currentAttachments.map((attachment) => (
              <View key={attachment.id} style={styles.currentAttachmentItem}>
                {attachment.type === 'image' ? (
                  <Image
                    source={{ uri: attachment.uri }}
                    style={styles.currentAttachmentImage}
                  />
                ) : (
                  <View style={styles.currentDocumentAttachment}>
                    <Text style={styles.currentDocumentName} numberOfLines={1}>
                      {attachment.name}
                    </Text>
                  </View>
                )}
                <TouchableOpacity
                  style={styles.removeAttachmentButton}
                  onPress={() => removeAttachment(attachment.id)}
                >
                  <Text style={styles.removeAttachmentText}>×</Text>
                </TouchableOpacity>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="Napíšte svoju otázku..."
          placeholderTextColor="#999"
          multiline
          maxLength={1000}
          editable={!isLoading}
        />

        {/* Tlačidlá pre prílohy */}
        <View style={styles.attachmentButtonsContainer}>
          <TouchableOpacity style={styles.attachmentButton} onPress={pickImage}>
            <Text style={styles.attachmentButtonText}>📷</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.attachmentButton} onPress={pickDocument}>
            <Text style={styles.attachmentButtonText}>📄</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[
              styles.sendButton,
              ((!inputText.trim() && currentAttachments.length === 0) || isLoading) && styles.sendButtonDisabled,
            ]}
            onPress={sendMessage}
            disabled={(!inputText.trim() && currentAttachments.length === 0) || isLoading}
          >
            <Text style={styles.sendButtonText}>Odoslať</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.clearButton,
              messages.length <= 1 && styles.clearButtonDisabled
            ]}
            onPress={clearChat}
            disabled={messages.length <= 1}
          >
            <Text style={[
              styles.clearButtonText,
              messages.length <= 1 && styles.clearButtonTextDisabled
            ]}>
              Vymazať
            </Text>
          </TouchableOpacity>
        </View>
      </View>
          </KeyboardAvoidingView>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  backgroundOverlay: {
    flex: 1,
    backgroundColor: 'rgba(26, 26, 26, 0.75)', // Zmenšená priehľadnosť aby bol obrázok viac viditeľný
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Zmenené z #1a1a1a na transparent
  },
  header: {
    backgroundColor: '#2d2d2d',
    paddingTop: Platform.OS === 'ios' ? 60 : 45,
    paddingBottom: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#404040',
  },
  headerTextContainer: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#999999',
    marginTop: 2,
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 8,
  },
  clearButton: {
    flex: 1,
    backgroundColor: '#ff4444',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearButtonDisabled: {
    backgroundColor: '#404040',
  },
  clearButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  clearButtonTextDisabled: {
    color: '#666666',
  },
  messagesContainer: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 120, // Ešte viac miesta pre input container
  },
  messageContainer: {
    marginVertical: 8,
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  userMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#007AFF',
  },
  aiMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#2d2d2d',
    borderWidth: 1,
    borderColor: '#404040',
  },
  messageText: {
    fontSize: 18,
    lineHeight: 26,
  },
  userMessageText: {
    color: '#ffffff',
  },
  aiMessageText: {
    color: '#ffffff',
  },
  timestamp: {
    fontSize: 14,
    color: '#999999',
    marginTop: 4,
    textAlign: 'right',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    backgroundColor: '#2d2d2d',
    padding: 12,
    borderRadius: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#404040',
  },
  loadingText: {
    color: '#999999',
    marginLeft: 8,
    fontSize: 16,
  },
  inputContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 30 : 20,
    left: 0,
    right: 0,
    padding: 16,
    backgroundColor: '#2d2d2d',
    borderTopWidth: 1,
    borderTopColor: '#404040',
  },
  textInput: {
    backgroundColor: '#1a1a1a',
    borderWidth: 1,
    borderColor: '#404040',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: '#ffffff',
    fontSize: 18,
    maxHeight: 100,
    marginBottom: 8,
  },
  sendButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#404040',
  },
  sendButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },

  // Štýly pre prílohy v správach
  attachmentsContainer: {
    marginBottom: 8,
  },
  attachmentItem: {
    marginBottom: 4,
  },
  attachmentImage: {
    width: 200,
    height: 150,
    borderRadius: 8,
    marginBottom: 4,
  },
  documentAttachment: {
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  documentType: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },

  // Štýly pre aktuálne prílohy
  currentAttachmentsContainer: {
    backgroundColor: '#f8f8f8',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  currentAttachmentItem: {
    marginRight: 8,
    position: 'relative',
  },
  currentAttachmentImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  currentDocumentAttachment: {
    width: 60,
    height: 60,
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  currentDocumentName: {
    fontSize: 10,
    color: '#333',
    textAlign: 'center',
    paddingHorizontal: 2,
  },
  removeAttachmentButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#ff4444',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeAttachmentText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },

  // Štýly pre tlačidlá príloh
  attachmentButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingVertical: 4,
    gap: 8,
  },
  attachmentButton: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    minWidth: 44,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  attachmentButtonText: {
    fontSize: 18,
  },
});
