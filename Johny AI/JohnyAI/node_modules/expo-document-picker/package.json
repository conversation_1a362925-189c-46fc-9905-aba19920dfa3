{"name": "expo-document-picker", "version": "13.1.6", "description": "Provides access to the system's UI for selecting documents from the available providers on the user's device.", "main": "build/index.js", "types": "build/index.d.ts", "sideEffects": false, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["react-native", "expo", "expo-document-picker", "document-picker"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-document-picker"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev/versions/latest/sdk/document-picker/", "jest": {"preset": "expo-module-scripts"}, "devDependencies": {"expo-module-scripts": "^4.1.7"}, "peerDependencies": {"expo": "*"}, "gitHead": "cc3b641cc2e4e7686dca75e7029cf76a07b3d647"}