{"version": 3, "file": "ExpoDocumentPicker.web.js", "sourceRoot": "", "sources": ["../src/ExpoDocumentPicker.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAI7C,eAAe;IACb,KAAK,CAAC,gBAAgB,CAAC,EACrB,IAAI,GAAG,KAAK,EACZ,QAAQ,GAAG,KAAK,EAChB,MAAM,GAAG,IAAI,GACS;QACtB,YAAY;QACZ,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YAC7B,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;QAC1C,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QAC7B,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACnC,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1E,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAChD,IAAI,QAAQ,EAAE,CAAC;YACb,KAAK,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;QAC7C,CAAC;QAED,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAEjC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;gBAC1C,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAChB,MAAM,OAAO,GAAmC,EAAE,CAAC;oBACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC5C,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;oBACtD,CAAC;oBACD,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;wBAC1C,OAAO,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC5D,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,MAAM,CAAC,CAAC,CAAC,CAAC;oBACZ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC5C,CAAC;gBAED,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACpC,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5C,CAAC,CAAC,CAAC;YAEH,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC;YACtC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAEF,SAAS,aAAa,CAAC,UAAgB,EAAE,SAAkB,IAAI;IAC7D,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,QAAQ,GAAG,UAAU,CAAC,IAAI,CAAC;QACjC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,CAAC;gBACN,GAAG,EAAE,UAAU,CAAC,kBAAkB;gBAClC,QAAQ;gBACR,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;QAChC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE;YACpB,MAAM,CAAC,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC,CAAC;QACvF,CAAC,CAAC;QACF,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;YAC7B,MAAM,GAAG,GAAI,MAAc,CAAC,MAAM,CAAC;YACnC,OAAO,CAAC;gBACN,GAAG;gBACH,QAAQ;gBACR,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,IAAI,EAAE,UAAU;aACjB,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,6CAA6C;QAC7C,MAAM,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACnC,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { Platform } from 'expo-modules-core';\n\nimport { DocumentPickerAsset, DocumentPickerOptions, DocumentPickerResult } from './types';\n\nexport default {\n  async getDocumentAsync({\n    type = '*/*',\n    multiple = false,\n    base64 = true,\n  }: DocumentPickerOptions): Promise<DocumentPickerResult> {\n    // SSR guard\n    if (!Platform.isDOMAvailable) {\n      return { canceled: true, assets: null };\n    }\n\n    const input = document.createElement('input');\n    input.style.display = 'none';\n    input.setAttribute('type', 'file');\n    input.setAttribute('accept', Array.isArray(type) ? type.join(',') : type);\n    input.setAttribute('id', String(Math.random()));\n    if (multiple) {\n      input.setAttribute('multiple', 'multiple');\n    }\n\n    document.body.appendChild(input);\n\n    return new Promise((resolve, reject) => {\n      input.addEventListener('change', async () => {\n        if (input.files) {\n          const results: Promise<DocumentPickerAsset>[] = [];\n          for (let i = 0; i < input.files.length; i++) {\n            results.push(readFileAsync(input.files[i], base64));\n          }\n          try {\n            const assets = await Promise.all(results);\n            resolve({ canceled: false, assets, output: input.files });\n          } catch (e) {\n            reject(e);\n          }\n        } else {\n          resolve({ canceled: true, assets: null });\n        }\n\n        document.body.removeChild(input);\n      });\n\n      input.addEventListener('cancel', () => {\n        resolve({ canceled: true, assets: null });\n      });\n\n      const event = new MouseEvent('click');\n      input.dispatchEvent(event);\n    });\n  },\n};\n\nfunction readFileAsync(targetFile: File, base64: boolean = true): Promise<DocumentPickerAsset> {\n  return new Promise((resolve, reject) => {\n    const mimeType = targetFile.type;\n    if (!base64) {\n      resolve({\n        uri: targetFile.webkitRelativePath,\n        mimeType,\n        name: targetFile.name,\n        lastModified: targetFile.lastModified,\n        size: targetFile.size,\n        file: targetFile,\n      });\n      return;\n    }\n    const reader = new FileReader();\n    reader.onerror = () => {\n      reject(new Error(`Failed to read the selected media because the operation failed.`));\n    };\n    reader.onload = ({ target }) => {\n      const uri = (target as any).result;\n      resolve({\n        uri,\n        mimeType,\n        name: targetFile.name,\n        lastModified: targetFile.lastModified,\n        size: targetFile.size,\n        file: targetFile,\n      });\n    };\n\n    // Read in the image file as a binary string.\n    reader.readAsDataURL(targetFile);\n  });\n}\n"]}