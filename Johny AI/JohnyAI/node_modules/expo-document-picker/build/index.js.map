{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,kBAAkB,MAAM,sBAAsB,CAAC;AAGtD,cAAc;AACd;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,EACrC,IAAI,GAAG,KAAK,EACZ,oBAAoB,GAAG,IAAI,EAC3B,QAAQ,GAAG,KAAK,EAChB,MAAM,GAAG,IAAI,MACY,EAAE;IAC3B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,IAAI,GAAG,CAAC,IAAI,CAAa,CAAC;IAC5B,CAAC;IACD,OAAO,MAAM,kBAAkB,CAAC,gBAAgB,CAAC;QAC/C,IAAI;QACJ,oBAAoB;QACpB,QAAQ;QACR,MAAM;KACP,CAAC,CAAC;AACL,CAAC;AAED,cAAc,SAAS,CAAC", "sourcesContent": ["import ExpoDocumentPicker from './ExpoDocumentPicker';\nimport { DocumentPickerOptions, DocumentPickerResult } from './types';\n\n// @needsAudit\n/**\n * Display the system UI for choosing a document. By default, the chosen file is copied to [the app's internal cache directory](filesystem/#filesystemcachedirectory).\n * > **Notes for Web:** The system UI can only be shown after user activation (e.g. a `Button` press).\n * > Therefore, calling `getDocumentAsync` in `componentDidMount`, for example, will **not** work as\n * > intended. The `cancel` event will not be returned in the browser due to platform restrictions and\n * > inconsistencies across browsers.\n *\n * @return On success returns a promise that fulfils with [`DocumentPickerResult`](#documentpickerresult) object.\n *\n * If the user cancelled the document picking, the promise resolves to `{ type: 'cancel' }`.\n */\nexport async function getDocumentAsync({\n  type = '*/*',\n  copyToCacheDirectory = true,\n  multiple = false,\n  base64 = true,\n}: DocumentPickerOptions = {}): Promise<DocumentPickerResult> {\n  if (typeof type === 'string') {\n    type = [type] as string[];\n  }\n  return await ExpoDocumentPicker.getDocumentAsync({\n    type,\n    copyToCacheDirectory,\n    multiple,\n    base64,\n  });\n}\n\nexport * from './types';\n"]}