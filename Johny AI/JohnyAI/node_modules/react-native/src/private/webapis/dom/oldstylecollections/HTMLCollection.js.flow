/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict
 */

import type {ArrayLike} from '../../utils/ArrayLikeUtils';

declare export default class HTMLCollection<+T>
  implements Iterable<T>, ArrayLike<T>
{
  // This property should've been read-only as well, but Flow doesn't handle
  // read-only indexers correctly (thinks reads are writes and fails).
  [index: number]: T;
  +length: number;
  item(index: number): T | null;
  namedItem(name: string): T | null;
  @@iterator(): Iterator<T>;
}

declare export function createHTMLCollection<T>(
  elements: $ReadOnlyArray<T>,
): HTMLCollection<T>;
