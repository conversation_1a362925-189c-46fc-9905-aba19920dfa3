{"name": "artur<PERSON>ai", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.16", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-dom": "19.0.0", "react-native-web": "^0.20.0", "@expo/metro-runtime": "~5.0.4", "expo-document-picker": "~13.1.6", "expo-image-picker": "~16.1.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}